import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import '../../../core/config/app_strings.dart' as AppStrings;
import '../../../core/services/exceptions.dart';
import '../../../domain/models/role.dart';
import '../../../domain/repository/office_staff_repository.dart';

part 'office_staff_state.dart';

class OfficeStaffCubit extends Cubit<OfficeStaffState> {
  OfficeStaffCubit(this._officeStaffRegisterRepository)
    : super(OfficeStaffInitial());
  final OfficeStaffRepository _officeStaffRegisterRepository;

  Future<void> registerOfficeStaff(Map<String, dynamic> requestBody) async {
    emit(OfficeStaffLoading());
    try {
      final response = await _officeStaffRegisterRepository.registerOfficeStaff(
        requestBody,
      );
      if (response is Map<String, dynamic>) {
        final userId = response['data'].toString();
        emit(OfficeStaffCreated(userId: userId, responseData: userId));
      } else {
        throw ApiException(
          message: AppStrings.invalidResponseFormat,
          statusCode: 500,
        );
      }
    } on ApiException catch (e) {
      emit(OfficeStaffError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        OfficeStaffError(
          message: '${AppStrings.unexpectedError}: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> uploadOfficeStaffFile(Map<String, dynamic> requestBody) async {
    try {
      final success = await _officeStaffRegisterRepository
          .uploadOfficeStaffFile(requestBody);
      if (success) {
        emit(OfficeStaffFileUploaded());
      } else {
        emit(OfficeStaffError(message: AppStrings.failedToUploadStaffFile));
      }
    } on ApiException catch (e) {
      emit(OfficeStaffError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        OfficeStaffError(
          message: '${AppStrings.unexpectedError}: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> getRoleListNames() async {
    emit(OfficeStaffLoading());
    try {
      final roleOptions = await _officeStaffRegisterRepository
          .getRoleListNames();
      emit(RoleLoaded(filterOptions: roleOptions));
    } on ApiException catch (e) {
      emit(OfficeStaffError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        OfficeStaffError(
          message: '${AppStrings.unexpectedError}: ${e.toString()}',
        ),
      );
    }
  }
}
