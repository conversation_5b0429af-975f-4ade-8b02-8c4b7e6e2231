part of 'office_staff_cubit.dart';

@immutable
sealed class OfficeStaffState {}

final class OfficeStaffInitial extends OfficeStaffState {}

final class OfficeStaffLoading extends OfficeStaffState {}

final class OfficeStaffCreated extends OfficeStaffState {
   final String? userId;
  final String? responseData;

  OfficeStaffCreated({this.userId, this.responseData});
}

final class OfficeStaffError extends OfficeStaffState {
  final String message;
  final int? statusCode;

  OfficeStaffError({required this.message, this.statusCode});
}

final class OfficeStaffFileUploaded extends OfficeStaffState {}

final class RoleLoaded extends OfficeStaffState {
  final List<Role> filterOptions;
  RoleLoaded({required this.filterOptions});
}

final class OfficeStaffRolesLoaded extends OfficeStaffState {
  final List<Role> roles;
  OfficeStaffRolesLoaded({required this.roles});
}
