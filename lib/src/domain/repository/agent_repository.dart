import '../models/agent_model.dart';
import '../models/filter_model.dart';
import '../models/role.dart';

abstract class AgentRepository {
  /// Get list of agents with POST request body
  Future<ApiAgentResponse> getAgents(Map<String, dynamic> requestBody);

  /// Get agent details by ID
  Future<AgentModel> getAgentById(String agentId);

  /// Create a new agent with POST request body
  Future<dynamic> registerAgent(Map<String, dynamic> requestBody);

  /// Upload agent file with POST request body
  Future<bool> uploadAgentFile(Map<String, dynamic> requestBody);

   //Role list
  Future<List<Role>> getRoleListNames();
}
